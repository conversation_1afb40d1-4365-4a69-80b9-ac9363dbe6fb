#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import logging
import argparse
import schedule
from datetime import datetime
from data_migration import DataMigrationService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('schedule_migration.log')
    ]
)
logger = logging.getLogger('schedule_migration')

def run_migration(env='dev'):
    """执行数据迁移任务"""
    try:
        logger.info(f"开始执行数据迁移任务，环境: {env}")
        start_time = datetime.now()
        
        service = DataMigrationService(env=env)
        service.run()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"数据迁移任务执行完成，耗时: {duration:.2f}秒")
    except Exception as e:
        logger.error(f"执行数据迁移任务失败: {str(e)}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据迁移定时任务')
    parser.add_argument('--env', choices=['dev', 'prod'], default='dev', help='运行环境 (dev 或 prod)')
    parser.add_argument('--schedule', choices=['hourly', 'daily', 'once'], default='daily', help='调度频率 (hourly, daily, once)')
    parser.add_argument('--time', default='01:00', help='每日执行时间 (格式: HH:MM，仅当schedule=daily时有效)')
    args = parser.parse_args()
    
    env = args.env
    schedule_type = args.schedule
    
    if schedule_type == 'once':
        # 立即执行一次
        run_migration(env)
    else:
        # 设置定时任务
        if schedule_type == 'hourly':
            logger.info(f"设置每小时执行一次数据迁移任务，环境: {env}")
            schedule.every().hour.do(run_migration, env)
        elif schedule_type == 'daily':
            time_parts = args.time.split(':')
            hour, minute = int(time_parts[0]), int(time_parts[1])
            logger.info(f"设置每天 {hour:02d}:{minute:02d} 执行数据迁移任务，环境: {env}")
            schedule.every().day.at(f"{hour:02d}:{minute:02d}").do(run_migration, env)
        
        # 运行调度器
        logger.info("调度器已启动")
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次

if __name__ == '__main__':
    main() 