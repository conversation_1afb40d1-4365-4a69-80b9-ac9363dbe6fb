# 数据迁移脚本使用说明

## 功能概述

数据迁移脚本现在支持通过 `--months-back` 参数设置向前扫描的月份数，可以灵活处理历史数据。

## 参数说明

- `--env`: 运行环境 (dev 或 prod)，默认为 dev
- `--debug`: 调试模式，不实际调用API
- `--log-level`: 日志级别 (DEBUG, INFO, WARNING, ERROR)，默认为 INFO
- `--months-back`: 向前扫描的月份数，默认为 1（上个月）

## 使用示例

### 1. 默认使用（只处理上个月数据）
```bash
python data_migration.py --env prod
```

### 2. 处理前2个月数据（您的需求：5月和6月）
```bash
python data_migration.py --env prod --months-back 2
```

### 3. 调试模式测试前2个月
```bash
python data_migration.py --env dev --debug --months-back 2 --log-level DEBUG
```

### 4. 处理前3个月数据
```bash
python data_migration.py --env prod --months-back 3
```

## 时间逻辑说明

假设当前时间是 2025年7月28日：

- `--months-back 1`: 扫描 2025年6月 (30天)
- `--months-back 2`: 扫描 2025年5月 + 2025年6月 (61天)
- `--months-back 3`: 扫描 2025年4月 + 2025年5月 + 2025年6月 (91天)

## Kubernetes CronJob 配置

当前配置为每月10号凌晨1点执行，处理前2个月的数据：

```yaml
spec:
  schedule: "0 1 10 * *"  # 每月10号凌晨1点
  # ...
  command:
    - python data_migration.py --env prod --months-back 2 --log-level INFO
```

## 执行计划

根据您的需求：

1. **当前（7月）**: 脚本会在7月10号执行，处理5月和6月的数据
2. **下个月（8月）**: 脚本会在8月10号执行，处理6月和7月的数据
3. **以此类推**: 每月10号执行，始终处理前2个月的数据

这样确保了：
- 历史数据（4月及之前）不会重复处理
- 从5月开始的数据会被正确处理
- 每月都会处理最近2个月的数据，确保数据完整性

## 注意事项

1. 脚本会自动处理跨年情况
2. 每个月的天数会自动计算（包括闰年2月）
3. 日志文件会按日期重命名保存
4. 支持调试模式，可以在不实际执行API调用的情况下测试逻辑
