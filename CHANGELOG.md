# 更新日志

## [1.0.0] - 2023-05-XX

### 新增
- 实现自动扫描数据集路径，查找包含`.complete`后缀的文件
- 支持两种日期格式的目录：`2024111312`和`20250513-00`
- 仅扫描最近7天的数据（可配置）
- 创建数据迁移记录
- 调用备份API创建备份任务
- 轮询监控备份任务状态并更新记录
- 支持定时执行（每小时、每天或单次执行）
- 支持调试模式，不实际调用API
- 支持Kubernetes CronJob部署

### 修复
- 路径处理逻辑，将`/cpfs01/projects-HDD/cfff-...`转换为`/cfff-...`，以符合API要求
- 增加异常处理，提高稳定性
- 优化日志输出，便于排查问题

### 优化
- 增加详细的日志记录
- 增加调试模式，便于测试
- 提供Docker和Kubernetes部署支持
- 增加启动和停止脚本，便于管理 