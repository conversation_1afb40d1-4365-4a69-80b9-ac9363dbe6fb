apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: '113'
    field.cattle.io/publicEndpoints: >-
      [{"port":32003,"protocol":"TCP","serviceName":"data-assets-prod-ns:assets-backend-dev-svc-nodeport","allNodes":true},{"addresses":["************"],"port":80,"protocol":"HTTP","serviceName":"data-assets-prod-ns:assets-backend-dev-svc-nodeport","ingressName":"data-assets-prod-ns:assets-backend-dev-svc-nodeport-ingress","hostname":"api-dev-assets.internal.sais.com.cn","path":"/","allNodes":false}]
  creationTimestamp: '2024-11-25T08:32:08Z'
  generation: 205
  labels:
    workload.user.cattle.io/workloadselector: apps.deployment-data-assets-prod-ns-assets-backend-dev-svc
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      workload.user.cattle.io/workloadselector: apps.deployment-data-assets-prod-ns-assets-backend-dev-svc
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        cattle.io/timestamp: '2024-11-29T08:52:17Z'
      creationTimestamp: null
      labels:
        workload.user.cattle.io/workloadselector: apps.deployment-data-assets-prod-ns-assets-backend-dev-svc
      name: assets-backend-dev-svc
      namespace: data-assets-prod-ns
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: kubernetes/app
                    operator: In
                    values:
                      - cpfsroot-zhaoxun
      containers:
        - env:
            - name: SPRING_PROFILES_ACTIVE
              value: dev
          image: harbor.internal.sais.com.cn/assets-platform/service-dev:1.1.8.8
          imagePullPolicy: Always
          name: container-0
          ports:
            - containerPort: 8080
              name: http8080
              protocol: TCP
            - containerPort: 5000
              name: http5000
              protocol: TCP
          resources: {}
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: false
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
          volumeMounts:
            - mountPath: /etc/nginx/nginx.conf
              name: nginx-config-volume
              subPath: nginx.conf
            - mountPath: /cpfs01/projects-HDD/AI4S_public_queue_2_HDD
              name: ai4s-public-queue-2-hdd
            - mountPath: /cpfs01/projects-SSD/AI4S_public_queue_2_SSD
              name: cfff-4a8d9af84f66-ssd
            - mountPath: /cpfs01/projects-SSD/cfff-4a8d9af84f66_SSD
              name: ai4s-public-queue-2-ssd
            - mountPath: /cpfs01/projects-HDD/cfff-4a8d9af84f66_HDD
              name: cfff-4a8d9af84f66-hdd
            - mountPath: /cpfs01/projects-HDD/cfff-c7cd658afc74_HDD
              name: cfff-c7cd658afc74-hdd
            - mountPath: /cpfs01/projects-SSD/cfff-c7cd658afc74_SSD
              name: cfff-c7cd658afc74-ssd
            - mountPath: /cpfs01/projects-HDD/cfff-282dafecea22_HDD
              name: cfff-282dafecea22-hdd
            - mountPath: /cpfs01/projects-SSD/cfff-282dafecea22_SSD
              name: cfff-282dafecea22-ssd
            - mountPath: /cpfs01/projects-HDD/cfff-4405968bce88_HDD
              name: cfff-4405968bce88-hdd
            - mountPath: /cpfs01/projects-SSD/cfff-4405968bce88_SSD
              name: cfff-4405968bce88-ssd
            - mountPath: /cpfs01/projects-HDD/cfff-01ff502a0784_HDD
              name: cfff-01ff502a0784-hdd
            - mountPath: /cpfs01/projects-HDD/cfff-85cad58c20e7_HDD
              name: cfff-85cad58c20e7-hdd
            - mountPath: /cpfs01/projects-SSD/cfff-6f3a36a0cd1e_SSD
              name: cfff-6f3a36a0cd1e-ssd
            - mountPath: /cpfs01/projects-HDD/cfff-6f3a36a0cd1e_HDD
              name: cfff-6f3a36a0cd1e-hdd
            - mountPath: /cpfs01/projects-HDD/cfff-f1bda9757d3c_HDD
              name: cfff-f1bda9757d3c-hdd
            - mountPath: /cpfs01/projects-SSD/cfff-f1bda9757d3c_SSD
              name: cfff-f1bda9757d3c-ssd
            - mountPath: /cpfs01/projects-SSD/cfff-01ff502a0784_SSD
              name: cfff-01ff502a0784-ssd
            - mountPath: /cpfs01/projects-HDD/cfff-3782eb030d9c_HDD
              name: cfff-3782eb030d9c-hdd
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      tolerations:
        - effect: NoSchedule
          key: dedicated
          operator: Equal
          value: crawTeamV2
      volumes:
        - configMap:
            defaultMode: 420
            name: assets-backend-dev-nginx-config
          name: nginx-config-volume
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/AI4S_public_queue_2_HDD
            type: ''
          name: ai4s-public-queue-2-hdd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-4a8d9af84f66_SSD
            type: ''
          name: cfff-4a8d9af84f66-ssd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/AI4S_public_queue_2_SSD
            type: ''
          name: ai4s-public-queue-2-ssd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-4a8d9af84f66_HDD
            type: ''
          name: cfff-4a8d9af84f66-hdd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-c7cd658afc74_HDD
            type: ''
          name: cfff-c7cd658afc74-hdd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-c7cd658afc74_SSD
            type: ''
          name: cfff-c7cd658afc74-ssd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-282dafecea22_HDD
            type: ''
          name: cfff-282dafecea22-hdd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-282dafecea22_SSD
            type: ''
          name: cfff-282dafecea22-ssd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-4405968bce88_HDD
            type: ''
          name: cfff-4405968bce88-hdd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-4405968bce88_SSD
            type: ''
          name: cfff-4405968bce88-ssd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-01ff502a0784_HDD
            type: ''
          name: cfff-01ff502a0784-hdd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-01ff502a0784_SSD
            type: ''
          name: cfff-01ff502a0784-ssd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-85cad58c20e7_HDD
            type: ''
          name: cfff-85cad58c20e7-hdd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-6f3a36a0cd1e_SSD
            type: ''
          name: cfff-6f3a36a0cd1e-ssd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-6f3a36a0cd1e_HDD
            type: ''
          name: cfff-6f3a36a0cd1e-hdd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-f1bda9757d3c_HDD
            type: ''
          name: cfff-f1bda9757d3c-hdd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-f1bda9757d3c_SSD
            type: ''
          name: cfff-f1bda9757d3c-ssd
        - hostPath:
            path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-3782eb030d9c_HDD
            type: ''
          name: cfff-3782eb030d9c-hdd
status:
  availableReplicas: 1
  conditions:
    - lastTransitionTime: '2024-11-25T08:32:08Z'
      lastUpdateTime: '2025-05-19T10:37:40Z'
      message: >-
        ReplicaSet "assets-backend-dev-svc-6f4d6b777d" has successfully
        progressed.
      reason: NewReplicaSetAvailable
      status: 'True'
      type: Progressing
    - lastTransitionTime: '2025-05-27T10:31:27Z'
      lastUpdateTime: '2025-05-27T10:31:27Z'
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: 'True'
      type: Available
  observedGeneration: 205
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1 



volumeMounts:
- mountPath: /etc/nginx/nginx.conf
    name: nginx-config-volume
    subPath: nginx.conf
- mountPath: /cpfs01/projects-HDD/AI4S_public_queue_2_HDD
    name: ai4s-public-queue-2-hdd
- mountPath: /cpfs01/projects-SSD/AI4S_public_queue_2_SSD
    name: cfff-4a8d9af84f66-ssd
- mountPath: /cpfs01/projects-SSD/cfff-4a8d9af84f66_SSD
    name: ai4s-public-queue-2-ssd
- mountPath: /cpfs01/projects-HDD/cfff-4a8d9af84f66_HDD
    name: cfff-4a8d9af84f66-hdd
- mountPath: /cpfs01/projects-HDD/cfff-c7cd658afc74_HDD
    name: cfff-c7cd658afc74-hdd
- mountPath: /cpfs01/projects-SSD/cfff-c7cd658afc74_SSD
    name: cfff-c7cd658afc74-ssd
- mountPath: /cpfs01/projects-HDD/cfff-282dafecea22_HDD
    name: cfff-282dafecea22-hdd
- mountPath: /cpfs01/projects-SSD/cfff-282dafecea22_SSD
    name: cfff-282dafecea22-ssd
- mountPath: /cpfs01/projects-HDD/cfff-4405968bce88_HDD
    name: cfff-4405968bce88-hdd
- mountPath: /cpfs01/projects-SSD/cfff-4405968bce88_SSD
    name: cfff-4405968bce88-ssd
- mountPath: /cpfs01/projects-HDD/cfff-01ff502a0784_HDD
    name: cfff-01ff502a0784-hdd
- mountPath: /cpfs01/projects-HDD/cfff-85cad58c20e7_HDD
    name: cfff-85cad58c20e7-hdd
- mountPath: /cpfs01/projects-SSD/cfff-6f3a36a0cd1e_SSD
    name: cfff-6f3a36a0cd1e-ssd
- mountPath: /cpfs01/projects-HDD/cfff-6f3a36a0cd1e_HDD
    name: cfff-6f3a36a0cd1e-hdd
- mountPath: /cpfs01/projects-HDD/cfff-f1bda9757d3c_HDD
    name: cfff-f1bda9757d3c-hdd
- mountPath: /cpfs01/projects-SSD/cfff-f1bda9757d3c_SSD
    name: cfff-f1bda9757d3c-ssd
- mountPath: /cpfs01/projects-SSD/cfff-01ff502a0784_SSD
    name: cfff-01ff502a0784-ssd
- mountPath: /cpfs01/projects-HDD/cfff-3782eb030d9c_HDD
    name: cfff-3782eb030d9c-hdd
volumes:
- configMap:
    defaultMode: 420
    name: assets-backend-dev-nginx-config
    name: nginx-config-volume
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/AI4S_public_queue_2_HDD
    type: ''
    name: ai4s-public-queue-2-hdd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-4a8d9af84f66_SSD
    type: ''
    name: cfff-4a8d9af84f66-ssd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/AI4S_public_queue_2_SSD
    type: ''
    name: ai4s-public-queue-2-ssd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-4a8d9af84f66_HDD
    type: ''
    name: cfff-4a8d9af84f66-hdd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-c7cd658afc74_HDD
    type: ''
    name: cfff-c7cd658afc74-hdd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-c7cd658afc74_SSD
    type: ''
    name: cfff-c7cd658afc74-ssd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-282dafecea22_HDD
    type: ''
    name: cfff-282dafecea22-hdd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-282dafecea22_SSD
    type: ''
    name: cfff-282dafecea22-ssd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-4405968bce88_HDD
    type: ''
    name: cfff-4405968bce88-hdd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-4405968bce88_SSD
    type: ''
    name: cfff-4405968bce88-ssd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-01ff502a0784_HDD
    type: ''
    name: cfff-01ff502a0784-hdd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-01ff502a0784_SSD
    type: ''
    name: cfff-01ff502a0784-ssd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-85cad58c20e7_HDD
    type: ''
    name: cfff-85cad58c20e7-hdd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-6f3a36a0cd1e_SSD
    type: ''
    name: cfff-6f3a36a0cd1e-ssd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-6f3a36a0cd1e_HDD
    type: ''
    name: cfff-6f3a36a0cd1e-hdd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-f1bda9757d3c_HDD
    type: ''
    name: cfff-f1bda9757d3c-hdd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-f1bda9757d3c_SSD
    type: ''
    name: cfff-f1bda9757d3c-ssd
- hostPath:
    path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-3782eb030d9c_HDD
    type: ''
    name: cfff-3782eb030d9c-hdd

还有
tolerations:
- effect: NoSchedule
    key: dedicated
    operator: Equal
    value: crawTeamV2 
affinity:
nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
        - matchExpressions:
            - key: kubernetes/app
            operator: In
            values:
                - cpfsroot-zhaoxun