#!/bin/bash

# 设置默认参数
ENV="dev"
SCHEDULE="daily"
TIME="01:00"

# 显示帮助信息
show_help() {
    echo "数据迁移服务启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -e, --env ENV       设置运行环境 (dev 或 prod)"
    echo "  -s, --schedule TYPE 设置调度类型 (hourly, daily, once)"
    echo "  -t, --time TIME     设置每日执行时间 (格式: HH:MM，仅当schedule=daily时有效)"
    echo "  -h, --help          显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -e prod -s hourly       # 在生产环境每小时执行一次"
    echo "  $0 -e dev -s daily -t 03:30  # 在开发环境每天3:30执行"
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENV="$2"
            shift 2
            ;;
        -s|--schedule)
            SCHEDULE="$2"
            shift 2
            ;;
        -t|--time)
            TIME="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证参数
if [[ "$ENV" != "dev" && "$ENV" != "prod" ]]; then
    echo "错误: 环境参数必须是 'dev' 或 'prod'"
    exit 1
fi

if [[ "$SCHEDULE" != "hourly" && "$SCHEDULE" != "daily" && "$SCHEDULE" != "once" ]]; then
    echo "错误: 调度类型必须是 'hourly', 'daily' 或 'once'"
    exit 1
fi

# 检查Python和依赖
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到python3命令"
    exit 1
fi

# 检查依赖是否已安装
echo "检查依赖..."
if ! python3 -c "import pymysql, requests, schedule" &> /dev/null; then
    echo "安装依赖..."
    pip install -r requirements.txt
fi

# 创建日志目录
LOG_DIR="logs"
mkdir -p $LOG_DIR

# 获取当前时间
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="${LOG_DIR}/migration_${ENV}_${TIMESTAMP}.log"

# 启动服务
echo "启动数据迁移服务..."
echo "环境: $ENV"
echo "调度类型: $SCHEDULE"
if [[ "$SCHEDULE" == "daily" ]]; then
    echo "执行时间: $TIME"
fi
echo "日志文件: $LOG_FILE"

# 如果是单次执行
if [[ "$SCHEDULE" == "once" ]]; then
    echo "执行单次任务..."
    python3 data_migration.py --env $ENV > $LOG_FILE 2>&1
    echo "任务执行完成，请查看日志: $LOG_FILE"
else
    # 后台运行定时任务
    nohup python3 schedule_migration.py --env $ENV --schedule $SCHEDULE --time $TIME > $LOG_FILE 2>&1 &
    PID=$!
    echo $PID > "${LOG_DIR}/migration_${ENV}.pid"
    echo "服务已在后台启动，PID: $PID"
    echo "使用 'kill $PID' 停止服务"
fi

echo "完成" 