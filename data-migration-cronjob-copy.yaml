apiVersion: batch/v1
kind: CronJob
metadata:
  name: data-migration-job
  namespace: data-assets-prod-ns  # 根据实际情况修改
spec:
  schedule: "0 1 * * *"  # 每天凌晨1点执行
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: data-migration
              image: harbor.internal.sais.com.cn/assets-platform/python:3.11-slim
              workingDir: /app
              command:
                - /bin/sh
                - -c
                - |
                  pip install -r requirements.txt && \
                  python data_migration.py --env dev --log-level DEBUG
              volumeMounts:
                - mountPath: /etc/nginx/nginx.conf
                  name: nginx-config-volume
                  subPath: nginx.conf
                - mountPath: /cpfs01/projects-HDD/AI4S_public_queue_2_HDD
                  name: ai4s-public-queue-2-hdd
                - mountPath: /cpfs01/projects-SSD/AI4S_public_queue_2_SSD
                  name: cfff-4a8d9af84f66-ssd
                - mountPath: /cpfs01/projects-SSD/cfff-4a8d9af84f66_SSD
                  name: ai4s-public-queue-2-ssd
                - mountPath: /cpfs01/projects-HDD/cfff-4a8d9af84f66_HDD
                  name: cfff-4a8d9af84f66-hdd
                - mountPath: /cpfs01/projects-HDD/cfff-c7cd658afc74_HDD
                  name: cfff-c7cd658afc74-hdd
                - mountPath: /cpfs01/projects-SSD/cfff-c7cd658afc74_SSD
                  name: cfff-c7cd658afc74-ssd
                - mountPath: /cpfs01/projects-HDD/cfff-282dafecea22_HDD
                  name: cfff-282dafecea22-hdd
                - mountPath: /cpfs01/projects-SSD/cfff-282dafecea22_SSD
                  name: cfff-282dafecea22-ssd
                - mountPath: /cpfs01/projects-HDD/cfff-4405968bce88_HDD
                  name: cfff-4405968bce88-hdd
                - mountPath: /cpfs01/projects-SSD/cfff-4405968bce88_SSD
                  name: cfff-4405968bce88-ssd
                - mountPath: /cpfs01/projects-HDD/cfff-01ff502a0784_HDD
                  name: cfff-01ff502a0784-hdd
                - mountPath: /cpfs01/projects-SSD/cfff-01ff502a0784_SSD
                  name: cfff-01ff502a0784-ssd
                - mountPath: /cpfs01/projects-HDD/cfff-85cad58c20e7_HDD
                  name: cfff-85cad58c20e7-hdd
                - mountPath: /cpfs01/projects-SSD/cfff-6f3a36a0cd1e_SSD
                  name: cfff-6f3a36a0cd1e-ssd
                - mountPath: /cpfs01/projects-HDD/cfff-6f3a36a0cd1e_HDD
                  name: cfff-6f3a36a0cd1e-hdd
                - mountPath: /cpfs01/projects-HDD/cfff-f1bda9757d3c_HDD
                  name: cfff-f1bda9757d3c-hdd
                - mountPath: /cpfs01/projects-SSD/cfff-f1bda9757d3c_SSD
                  name: cfff-f1bda9757d3c-ssd
                - mountPath: /cpfs01/projects-HDD/cfff-3782eb030d9c_HDD
                  name: cfff-3782eb030d9c-hdd
          volumes:
            - name: nginx-config-volume
              configMap:
                name: assets-backend-dev-nginx-config
                defaultMode: 420
            - name: ai4s-public-queue-2-hdd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/AI4S_public_queue_2_HDD
            - name: ai4s-public-queue-2-ssd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/AI4S_public_queue_2_SSD
            - name: cfff-4a8d9af84f66-ssd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-4a8d9af84f66_SSD
            - name: cfff-4a8d9af84f66-hdd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-4a8d9af84f66_HDD
            - name: cfff-c7cd658afc74-hdd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-c7cd658afc74_HDD
            - name: cfff-c7cd658afc74-ssd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-c7cd658afc74_SSD
            - name: cfff-282dafecea22-hdd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-282dafecea22_HDD
            - name: cfff-282dafecea22-ssd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-282dafecea22_SSD
            - name: cfff-4405968bce88-hdd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-4405968bce88_HDD
            - name: cfff-4405968bce88-ssd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-4405968bce88_SSD
            - name: cfff-01ff502a0784-hdd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-01ff502a0784_HDD
            - name: cfff-01ff502a0784-ssd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-01ff502a0784_SSD
            - name: cfff-85cad58c20e7-hdd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-85cad58c20e7_HDD
            - name: cfff-6f3a36a0cd1e-ssd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-6f3a36a0cd1e_SSD
            - name: cfff-6f3a36a0cd1e-hdd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-6f3a36a0cd1e_HDD
            - name: cfff-f1bda9757d3c-hdd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-f1bda9757d3c_HDD
            - name: cfff-f1bda9757d3c-ssd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-f1bda9757d3c_SSD
            - name: cfff-3782eb030d9c-hdd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-3782eb030d9c_HDD
          restartPolicy: OnFailure
          tolerations:
            - key: dedicated
              operator: Equal
              value: crawTeamV2
              effect: NoSchedule
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: kubernetes/app
                        operator: In
                        values:
                          - cpfsroot-zhaoxun
