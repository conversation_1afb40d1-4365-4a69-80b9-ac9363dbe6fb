# 数据迁移自动化脚本

这个脚本用于自动扫描指定数据集路径下的数据，查找包含`.complete`后缀的文件，并创建备份任务。

## 功能特点

- 自动连接StarRocks数据库（支持dev和prod环境）
- 查询带有特定标签的数据集信息
- 扫描数据集路径，查找包含`.complete`后缀的文件
- 扫描上一个完整月的数据（例如：6月运行时，扫描5月1日至5月31日的数据）
- 创建数据迁移记录
- 调用备份API创建备份任务
- 轮询监控备份任务状态并更新记录
- 支持定时执行（每小时、每天或单次执行）
- 支持调试模式，不实际调用API
- 支持Kubernetes CronJob部署

## 环境要求

- Python 3.6+
- 依赖包：
  - pymysql
  - requests
  - schedule

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 使用启动脚本（推荐）

```bash
# 赋予脚本执行权限
chmod +x start_migration_service.sh
chmod +x stop_migration_service.sh

# 开发环境每天凌晨1点执行
./start_migration_service.sh -e dev -s daily -t 01:00

# 生产环境每小时执行
./start_migration_service.sh -e prod -s hourly

# 立即执行一次（开发环境）
./start_migration_service.sh -e dev -s once

# 停止服务
./stop_migration_service.sh -e dev  # 停止开发环境的服务
./stop_migration_service.sh -e prod  # 停止生产环境的服务
```

### 单次执行

```bash
# 开发环境
python data_migration.py --env dev

# 生产环境
python data_migration.py --env prod

# 调试模式（不实际调用API）
python data_migration.py --env dev --debug

# 设置日志级别
python data_migration.py --env dev --log-level DEBUG
```

### 定时执行

```bash
# 每天凌晨1点执行（开发环境）
python schedule_migration.py --env dev --schedule daily --time 01:00

# 每小时执行一次（生产环境）
python schedule_migration.py --env prod --schedule hourly

# 立即执行一次（生产环境）
python schedule_migration.py --env prod --schedule once
```

### 使用Docker

docker build --platform linux/amd64 -t harbor-paas.internal.sais.com.cn/assets-platform/data_migration_py:1.0.1 --push .

```bash
# 构建镜像
docker build -t data-migration:latest .

# 运行容器（开发环境）
docker run -v /cpfs01:/cpfs01 data-migration:latest --env dev

# 运行容器（生产环境）
docker run -v /cpfs01:/cpfs01 data-migration:latest --env prod --log-level INFO

# 调试模式
docker run -v /cpfs01:/cpfs01 data-migration:latest --env dev --debug
```

### 使用Kubernetes CronJob

1. 创建ConfigMap，包含所有脚本文件

```bash
kubectl create configmap data-migration-files \
  --from-file=data_migration.py \
  --from-file=requirements.txt \
  -n data-assets-prod-ns
```

2. 应用CronJob配置

```bash
kubectl apply -f data-migration-cronjob.yaml
```

3. 手动触发一次任务

```bash
kubectl create job --from=cronjob/data-migration-job data-migration-manual -n data-assets-prod-ns
```

4. 查看日志

```bash
# 获取最新的Job
JOB_POD=$(kubectl get pods -n data-assets-prod-ns -l job-name -o jsonpath='{.items[0].metadata.name}')

# 查看日志
kubectl logs $JOB_POD -n data-assets-prod-ns
```

## 配置说明

脚本中的配置项可以根据需要进行调整：

- `DB_CONFIG`: 数据库连接配置
- `API_CONFIG`: API接口配置
- `SCAN_CONFIG`: 扫描配置
  - `poll_interval`: 轮询间隔（默认10秒）
  - `max_retries`: 最大轮询次数（默认60次，即10分钟）

## 路径处理

脚本会自动将路径格式从`/cpfs01/projects-HDD/cfff-...`转换为`/cfff-...`，以符合API的要求。例如：

- 输入路径：`/cpfs01/projects-HDD/cfff-4a8d9af84f66_HDD/public/database/fuxi/c88/history/era5_fc0_merged/1h_sr`
- 转换后：`/cfff-4a8d9af84f66_HDD/public/database/fuxi/c88/history/era5_fc0_merged/1h_sr`

## 调试模式

调试模式是一个非常有用的功能，可以帮助您在不实际调用API或修改数据库的情况下测试脚本逻辑。这对于开发和测试阶段非常有用。

### 开启调试模式

```bash
# 使用Python脚本
python data_migration.py --env dev --debug --log-level DEBUG

# 或使用启动脚本
./start_migration_service.sh -e dev -s once --debug
```

### 调试模式特点

1. 不会实际连接数据库或执行SQL语句
2. 不会实际调用备份API
3. 模拟API响应和数据库操作
4. 输出详细的日志信息，便于排查问题

### 何时使用调试模式

- 开发新功能时
- 修复bug时
- 测试路径处理逻辑
- 验证扫描逻辑是否正确
- 不想实际触发备份任务但想验证脚本流程

## 三方API状态说明

三方备份工具API返回的任务状态有多种，脚本会直接使用这些状态并更新到数据库中：

| 状态 | 说明 | 处理方式 |
|------|------|---------|
| hot2Warm | 正在从热存储迁移到温存储 | 视为进行中状态 |
| warm2Hot | 正在从温存储迁移到热存储 | 视为进行中状态 |
| cold2Warm | 正在从冷存储迁移到温存储 | 视为进行中状态 |
| warm2Cold | 正在从温存储迁移到冷存储 | 视为进行中状态 |
| deleteWarm | 正在删除温存储数据 | 视为进行中状态 |
| completed | 任务已完成 | 更新为success状态 |
| failed | 任务失败 | 更新为failed状态并记录错误信息 |

## 日志

- 主脚本日志：`data_migration.log`
- 定时任务日志：`schedule_migration.log`
- 使用启动脚本时的日志：`logs/migration_<env>_<timestamp>.log`

## 数据库表结构

脚本使用的数据库表结构如下：

```sql
CREATE TABLE `data_migration_record` (
  `id` bigint(20) NOT NULL COMMENT "主键ID",
  `dataset_id` bigint(20) NOT NULL COMMENT "数据集ID",
  `create_by` int(11) NOT NULL COMMENT "发起人",
  `workflow_id` varchar(64) NULL COMMENT "工作流ID(任务ID)",
  `operation_type` tinyint(4) NULL COMMENT "操作类型(1:备份,2:取回)",
  `source_path` varchar(512) NULL COMMENT "备份路径",
  `dest_path` varchar(512) NULL COMMENT "目标路径(取回时需作为使用)",
  `data_version` int(11) NULL COMMENT "数据版本",
  `source_flag` tinyint(4) NULL DEFAULT "0" COMMENT "数据来源标识：0表示默认，1表示自动化程序",
  `status` varchar(16) NULL COMMENT "任务状态(1:成功,2:失败)",
  `progress` varchar(8) NULL COMMENT "进度百分比",
  `error_message` varchar(2555) NULL COMMENT "错误信息",
  `start_time` datetime NULL COMMENT "开始时间",
  `end_time` datetime NULL COMMENT "结束时间",
  `cre_dt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
  `upd_dt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "更新时间"
) ENGINE=OLAP 
PRIMARY KEY(`id`)
```

## 注意事项

1. 脚本扫描上一个完整月的数据，例如6月运行时，扫描5月1日至5月31日的数据
2. 脚本支持两种日期格式的目录：`2024111312`和`20250513-00`
3. 备份任务创建后，脚本会每10秒轮询一次任务状态，最多轮询60次（10分钟）
4. 如果轮询超时，任务状态会被标记为`timeout`
5. 定时任务使用`schedule`库实现，支持每小时、每天或单次执行
6. 定时任务需要保持进程运行，建议使用启动脚本在后台运行
7. 启动脚本会自动创建`logs`目录并将日志保存在该目录下
8. 停止脚本会优雅地停止服务，如果进程在10秒内未能停止，将尝试强制终止
9. 在Kubernetes环境中，推荐使用CronJob而不是脚本内部的调度功能
10. 调试模式下不会实际调用API或修改数据库，适合用于测试脚本逻辑


调试模式测试：
python data_migration.py --env dev --debug --log-level DEBUG


main() → DataMigrationService.run() → 
├── 获取数据集 (get_datasets)
├── 扫描目录 (scan_directory_for_complete_files) 
├── 创建迁移记录 (create_migration_record)
├── 创建备份任务 (create_backup_job)
└── 轮询任务状态 (poll_job_status)