#!/bin/bash

# 设置默认参数
ENV="dev"

# 显示帮助信息
show_help() {
    echo "数据迁移服务停止脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -e, --env ENV       设置运行环境 (dev 或 prod)"
    echo "  -h, --help          显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -e prod       # 停止生产环境的服务"
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENV="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证参数
if [[ "$ENV" != "dev" && "$ENV" != "prod" ]]; then
    echo "错误: 环境参数必须是 'dev' 或 'prod'"
    exit 1
fi

# 检查PID文件
LOG_DIR="logs"
PID_FILE="${LOG_DIR}/migration_${ENV}.pid"

if [[ ! -f "$PID_FILE" ]]; then
    echo "错误: 未找到PID文件，服务可能未运行"
    exit 1
fi

# 读取PID
PID=$(cat $PID_FILE)

if [[ -z "$PID" ]]; then
    echo "错误: PID文件为空"
    rm -f $PID_FILE
    exit 1
fi

# 检查进程是否存在
if ! ps -p $PID > /dev/null; then
    echo "警告: 进程 $PID 不存在，可能已经停止"
    rm -f $PID_FILE
    exit 0
fi

# 停止服务
echo "停止数据迁移服务 (PID: $PID)..."
kill $PID

# 等待进程结束
MAX_WAIT=10
WAIT_COUNT=0
while ps -p $PID > /dev/null && [[ $WAIT_COUNT -lt $MAX_WAIT ]]; do
    echo "等待进程结束..."
    sleep 1
    WAIT_COUNT=$((WAIT_COUNT + 1))
done

# 检查进程是否已经停止
if ps -p $PID > /dev/null; then
    echo "警告: 进程未能在 $MAX_WAIT 秒内停止，尝试强制终止..."
    kill -9 $PID
    sleep 1
fi

# 最终检查
if ps -p $PID > /dev/null; then
    echo "错误: 无法停止进程 $PID"
    exit 1
else
    echo "服务已停止"
    rm -f $PID_FILE
fi

echo "完成" 