apiVersion: batch/v1
kind: CronJob
metadata:
  name: data-migration-job
  namespace: data-assets-prod-ns  # 根据实际情况修改
spec:
  schedule: "0 1 10 * *"  
  concurrencyPolicy: Forbid  # 不允许并发执行
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: data-migration
            image: harbor.internal.sais.com.cn/assets-platform/python:3.11-slim
            workingDir: /app
            command:
            - /bin/sh
            - -c
            - |
              pip install -r requirements.txt && \
              python data_migration.py --env prod --months-back 2 --log-level INFO
            volumeMounts:
              - mountPath: /app
                name: app-volume
              - mountPath: /cpfs01/projects-HDD/cfff-85cad58c20e7_HDD
                name: cfff-85cad58c20e7-hdd
              # 根据需要添加其他挂载点
          volumes:
            - name: app-volume
              configMap:
                name: data-migration-files
                defaultMode: 0755
            - name: cfff-85cad58c20e7-hdd
              hostPath:
                path: /f9039ff7-f979-4b43-8a3f-52d6461e7515/cfff-85cad58c20e7_HDD
                type: Directory
            # 根据需要添加其他卷
          restartPolicy: OnFailure 
