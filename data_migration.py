#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import datetime
import json
import logging
import pymysql
import requests
import re
import calendar
import shutil
from typing import List, Dict, Tuple, Optional

# 创建日志目录
LOG_DIR = '/app/logs/data_migration'
LOG_FILE = 'data_migration.log'
LOG_PATH = os.path.join(LOG_DIR, LOG_FILE)

# 确保日志目录存在
os.makedirs(LOG_DIR, exist_ok=True)

# 检查是否存在当天的日志文件，如果存在则删除
today = datetime.datetime.now().strftime('%Y%m%d')
dated_log_file = f'data_migration-{today}.log'
dated_log_path = os.path.join(LOG_DIR, dated_log_file)

if os.path.exists(dated_log_path):
    try:
        os.remove(dated_log_path)
        print(f"删除已存在的当天日志文件: {dated_log_path}")
    except Exception as e:
        print(f"删除当天日志文件失败: {str(e)}")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(LOG_PATH)
    ]
)
logger = logging.getLogger('data_migration')

# 数据库配置
DB_CONFIG = {
    'dev': {
        'host': '************',
        'port': 30000,
        'user': 'root',
        'password': '',
        'db': 'CZY_DATA_ASSETS_META_DEV'
    },
    'prod': {
        'host': 'kube-starrocks-fe-search.data-infra-prod-ns',
        'port': 9030,
        'user': 'root',
        'password': '',
        'db': 'CZY_DATA_ASSETS_META_PROD'
    }
}

# API配置
API_CONFIG = {
    'dev': {
        'backup_url': 'http://turbotransitplatform-dev.internal.sais.com.cn/turbotransitplatform/v1/backupjobs/',
        'auth_token': 'sk_123456789',
        'user_group_token': 'a1b9c608f0059a83ec2cb17cc37b9dd2'
    },
    'prod': {
        'backup_url': 'http://turbotransitplatform.internal.sais.com.cn/turbotransitplatform/v1/backupjobs/',
        'auth_token': 'sk_123456789',
        'user_group_token': 'a1b9c608f0059a83ec2cb17cc37b9dd2'
    }
}

# 扫描配置
SCAN_CONFIG = {
    'poll_interval': 10,  # 轮询间隔（秒）
    'max_retries': 90,   # 最大轮询次数（10秒 * 90 = 900秒 = 15分钟）
}

def rename_log_file():
    """将当前日志文件重命名为带日期的格式"""
    try:
        today = datetime.datetime.now().strftime('%Y%m%d')
        dated_log_file = f'data_migration-{today}.log'
        dated_log_path = os.path.join(LOG_DIR, dated_log_file)
        
        # 如果目标文件已存在，先删除
        if os.path.exists(dated_log_path):
            os.remove(dated_log_path)
            logger.info(f"删除已存在的日期日志文件: {dated_log_path}")
        
        # 重命名当前日志文件
        if os.path.exists(LOG_PATH):
            shutil.copy2(LOG_PATH, dated_log_path)
            logger.info(f"日志文件已复制: {LOG_PATH} -> {dated_log_path}")

            # 复制完成后删除原始日志文件
            os.remove(LOG_PATH)
            logger.info(f"原始日志文件已删除: {LOG_PATH}")
        else:
            logger.warning(f"当前日志文件不存在: {LOG_PATH}")
    except Exception as e:
        logger.error(f"重命名日志文件失败: {str(e)}")

class DataMigrationService:
    def __init__(self, env='dev', debug_mode=False, months_back=1):
        self.env = env
        self.debug_mode = debug_mode
        self.months_back = months_back  # 向前扫描的月份数
        self.db_config = DB_CONFIG[env]
        self.api_config = API_CONFIG[env]
        self.conn = None
        self.cursor = None

        # 任务统计相关
        self.total_datasets = 0                # 需要处理的数据集总数
        self.processed_datasets = 0            # 已处理的数据集数
        self.dataset_stats = {}                # 每个数据集的统计信息

        logger.info(f"初始化数据迁移服务，环境: {env}, 调试模式: {'开启' if debug_mode else '关闭'}, 扫描前{months_back}个月")
        
    def connect_db(self):
        """连接数据库"""
        try:
            self.conn = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['db']
            )
            self.cursor = self.conn.cursor(pymysql.cursors.DictCursor)
            logger.info(f"成功连接到{self.env}环境数据库")
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise
    
    def close_db(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        logger.info("数据库连接已关闭")
    
    def get_datasets(self) -> List[Dict]:
        """获取需要迁移的数据集信息"""
        try:
            query = """
            SELECT id,
                json_query(ref_path, '$[0]') AS first_path
            FROM meta_datasets
            WHERE array_contains(cast(tag AS ARRAY<STRING>), '3300001')
            """
            self.cursor.execute(query)
            results = self.cursor.fetchall()
            logger.info(f"获取到{len(results)}个数据集")
            return results
        except Exception as e:
            logger.error(f"获取数据集信息失败: {str(e)}")
            raise
    
    def convert_path_for_api(self, path: str) -> str:
        """转换路径为API可接受的格式（/cfff-开头）"""
        # 使用正则表达式匹配 /cpfs01/projects-HDD/cfff- 开头的路径
        match = re.match(r'^/cpfs01/projects-HDD(/cfff-.*)', path)
        if match:
            api_path = match.group(1)
            logger.info(f"路径转换: {path} -> {api_path}")
            return api_path
        
        logger.warning(f"路径格式不符合预期，无法转换: {path}")
        return path
    
    def scan_directory_for_complete_files(self, base_path: str, months_back: int = 1) -> List[str]:
        """扫描目录寻找包含.complete文件的路径

        Args:
            base_path: 基础扫描路径
            months_back: 向前扫描的月份数，默认为1（上个月）
        """
        complete_paths = []

        if not os.path.exists(base_path):
            logger.warning(f"路径不存在: {base_path}")
            return complete_paths

        # 获取需要扫描的月份列表
        today = datetime.datetime.now()
        scan_dates = []
        target_years = set()

        logger.info(f"开始扫描前{months_back}个月的数据")

        for month_offset in range(1, months_back + 1):
            # 计算目标月份
            target_month = today.month - month_offset
            target_year = today.year

            # 处理跨年情况
            while target_month <= 0:
                target_month += 12
                target_year -= 1

            # 获取该月的天数
            last_day_of_month = calendar.monthrange(target_year, target_month)[1]

            # 生成该月每一天的日期字符串
            month_dates = []
            for day in range(1, last_day_of_month + 1):
                date_obj = datetime.datetime(target_year, target_month, day)
                month_dates.append(date_obj.strftime('%Y%m%d'))

            scan_dates.extend(month_dates)
            target_years.add(str(target_year))

            logger.info(f"添加扫描月份: {target_year}年{target_month}月 ({len(month_dates)}天)")

        # 转换为列表并排序
        target_years = list(target_years)
        scan_dates.sort()

        logger.info(f"总扫描日期范围: {scan_dates[0]} 至 {scan_dates[-1]}, 共{len(scan_dates)}天")
        logger.info(f"目标年份目录: {target_years}")
        
        try:
            # 获取所有年份目录
            all_year_dirs = [d for d in os.listdir(base_path) if re.match(r'^\d{4}$', d)]
            logger.info(f"找到年份目录: {', '.join(all_year_dirs)}")
            
            # 只处理目标年份的目录
            year_dirs = [d for d in all_year_dirs if d in target_years]
            logger.info(f"将处理的年份目录: {', '.join(year_dirs)}")
            
            for year_dir in year_dirs:
                year_path = os.path.join(base_path, year_dir)
                if not os.path.isdir(year_path):
                    continue
                    
                # 获取年份目录下的所有子目录
                sub_dirs = os.listdir(year_path)
                logger.debug(f"在{year_path}下找到子目录: {len(sub_dirs)}个")
                
                for sub_dir in sub_dirs:
                    sub_path = os.path.join(year_path, sub_dir)
                    if not os.path.isdir(sub_path):
                        continue
                    
                    # 检查是否是上个月的数据
                    is_target_month = False
                    for date_str in scan_dates:
                        # 检查两种格式: 2024111312 或 20250513-00
                        if sub_dir.startswith(date_str) or (len(sub_dir) >= 8 and sub_dir.split('-')[0] in scan_dates):
                            is_target_month = True
                            break
                    
                    if not is_target_month:
                        continue
                    
                    # 检查目录中是否有.complete文件
                    try:
                        files = os.listdir(sub_path)
                        complete_files = [f for f in files if f.endswith('.complete')]
                        
                        if complete_files:
                            logger.info(f"找到包含.complete文件的路径: {sub_path}")
                            logger.info(f".complete文件: {', '.join(complete_files)}")
                            complete_paths.append(sub_path)
                    except Exception as e:
                        logger.error(f"读取目录{sub_path}失败: {str(e)}")
        except Exception as e:
            logger.error(f"扫描目录{base_path}失败: {str(e)}")
        
        logger.info(f"共找到{len(complete_paths)}个包含.complete文件的路径")
        return complete_paths
    
    def create_migration_record(self, dataset_id: int, source_path: str) -> int:
        """创建迁移记录"""
        try:
            # 转换路径为API可接受的格式
            api_path = self.convert_path_for_api(source_path)
            
            # 生成唯一ID
            current_time = int(time.time() * 1000)
            record_id = int(f"{dataset_id}{current_time % 10000000}")
            
            query = """
            INSERT INTO data_migration_record (
                id, dataset_id, create_by, operation_type, source_path,
                data_version, source_flag, status, start_time, cre_dt, upd_dt
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW()
            )
            """
            
            values = (
                record_id,             # id
                dataset_id,            # dataset_id
                0,                     # create_by (系统自动创建)
                1,                     # operation_type (1:备份)
                api_path,              # source_path (已转换为API格式)
                1,                     # data_version
                1,                     # source_flag (1:自动化程序)
                'pending',             # status
                datetime.datetime.now()  # start_time
            )
            
            if self.debug_mode:
                logger.info(f"调试模式: 创建迁移记录 (dataset_id={dataset_id}, source_path={api_path})")
            else:
                self.cursor.execute(query, values)
                self.conn.commit()
                logger.info(f"创建迁移记录成功, ID: {record_id}")
            
            return record_id
            
        except Exception as e:
            if not self.debug_mode and self.conn:
                self.conn.rollback()
            logger.error(f"创建迁移记录失败: {str(e)}")
            raise
    
    def create_backup_job(self, source_path: str, dataset_id: int) -> Dict:
        """创建备份任务"""
        try:
            url = self.api_config['backup_url']
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': f"Bearer {self.api_config['auth_token']}",
                'user_group_token': self.api_config['user_group_token']
            }
            
            payload = {
                'source_path': source_path,  # 已在create_migration_record中转换
                'data_id': str(dataset_id),
                'data_version': 1,
                'delete_source': False
            }
            
            if self.debug_mode:
                logger.info(f"调试模式: 创建备份任务 (URL={url})")
                logger.info(f"调试模式: 请求头: {headers}")
                logger.info(f"调试模式: 请求体: {payload}")
                # 模拟返回结果
                mock_result = {
                    'status': 200,
                    'msg': 'Backup job created successfully',
                    'data': {
                        'workflow_id': f'mock-workflow-{int(time.time())}',
                        'status': 'pending',
                        'progress': '0'
                    }
                }
                logger.info(f"调试模式: 模拟返回结果: {mock_result}")
                return mock_result
            else:
                response = requests.post(url, headers=headers, data=json.dumps(payload))
                response.raise_for_status()
                result = response.json()
                logger.info(f"创建备份任务成功: {result}")
                return result
            
        except Exception as e:
            logger.error(f"创建备份任务失败: {str(e)}")
            raise
    
    def update_migration_record(self, record_id: int, workflow_id: str = None, 
                               status: str = None, progress: str = None, 
                               error_message: str = None, end_time: bool = False) -> None:
        """更新迁移记录"""
        try:
            update_fields = []
            values = []
            
            if workflow_id:
                update_fields.append("workflow_id = %s")
                values.append(workflow_id)
            
            if status:
                update_fields.append("status = %s")
                values.append(status)
            
            if progress:
                update_fields.append("progress = %s")
                values.append(progress)
            
            if error_message:
                update_fields.append("error_message = %s")
                values.append(error_message)
            
            if end_time:
                update_fields.append("end_time = NOW()")
            
            update_fields.append("upd_dt = NOW()")
            
            if not update_fields:
                return
            
            query = f"UPDATE data_migration_record SET {', '.join(update_fields)} WHERE id = %s"
            values.append(record_id)
            
            if self.debug_mode:
                logger.info(f"调试模式: 更新迁移记录 (ID={record_id})")
                logger.info(f"调试模式: SQL: {query}")
                logger.info(f"调试模式: 参数: {values}")
            else:
                self.cursor.execute(query, values)
                self.conn.commit()
                logger.info(f"更新迁移记录成功, ID: {record_id}")
            
        except Exception as e:
            if not self.debug_mode and self.conn:
                self.conn.rollback()
            logger.error(f"更新迁移记录失败: {str(e)}")
            raise
    
    def check_job_status(self, workflow_id: str) -> Dict:
        """检查任务状态"""
        try:
            url = f"{self.api_config['backup_url']}?workflow_id={workflow_id}"
            headers = {
                'Accept': 'application/json',
                'Authorization': f"Bearer {self.api_config['auth_token']}",
                'user_group_token': self.api_config['user_group_token']
            }
            
            if self.debug_mode:
                logger.info(f"调试模式: 检查任务状态 (workflow_id={workflow_id})")
                # 模拟状态变化
                statuses = ['pending', 'running', 'running', 'completed']
                progress_values = ['0', '30', '70', '100']
                
                # 使用workflow_id的哈希值来确定模拟状态
                hash_val = hash(workflow_id) % 10
                if hash_val < 8:  # 80%成功率
                    idx = min(3, int(time.time() % 4))
                    mock_result = {
                        'status': 200,
                        'msg': '查询成功',
                        'data': {
                            'workflow_id': workflow_id,
                            'status': statuses[idx],
                            'progress': progress_values[idx] + '%'
                        }
                    }
                else:  # 20%失败率
                    mock_result = {
                        'status': 200,
                        'msg': '查询成功',
                        'data': {
                            'workflow_id': workflow_id,
                            'status': 'failed',
                            'progress': '50%',
                            'error_message': '模拟失败场景'
                        }
                    }
                
                logger.info(f"调试模式: 模拟任务状态: {mock_result}")
                return mock_result['data']
            else:
                response = requests.get(url, headers=headers)
                response.raise_for_status()
                result = response.json()
                if result['status'] == 200 and 'data' in result:
                    logger.info(f"检查任务状态: {workflow_id}, 状态: {result['data'].get('status')}, 进度: {result['data'].get('progress')}")
                    return result['data']
                else:
                    logger.error(f"检查任务状态失败: {result}")
                    raise Exception(f"检查任务状态失败: {result['msg'] if 'msg' in result else '未知错误'}")
            
        except Exception as e:
            logger.error(f"检查任务状态失败: {str(e)}")
            raise
    
    def poll_job_status(self, record_id: int, workflow_id: str) -> str:
        """轮询任务状态，并返回最终状态"""
        if self.debug_mode:
            logger.info(f"调试模式: 模拟轮询任务状态 (workflow_id={workflow_id})")
            # 模拟成功状态
            self.update_migration_record(record_id, status='completed', progress='100', end_time=True)
            return 'completed'
            
        retries = 0
        final_status = 'unknown'
        
        while retries < SCAN_CONFIG['max_retries']:
            try:
                job_status = self.check_job_status(workflow_id)
                status = job_status.get('status', '').lower()
                progress = job_status.get('progress', '0')
                
                logger.info(f"任务状态: {status}, 进度: {progress}")
                
                # 直接使用三方API返回的状态和进度
                self.update_migration_record(record_id, status=status, progress=progress)
                
                # 检查是否完成
                if status.lower() == 'completed':
                    logger.info(f"任务完成: {workflow_id}")
                    self.update_migration_record(record_id, status='completed', end_time=True)
                    final_status = 'completed'
                    return final_status
                elif status.lower() == 'failed':
                    error_message = job_status.get('error_message', job_status.get('message', '未知错误'))
                    logger.error(f"任务失败: {workflow_id}, 错误: {error_message}")
                    self.update_migration_record(record_id, status='failed', error_message=error_message, end_time=True)
                    final_status = 'failed'
                    return final_status
                # 其他状态都视为进行中
                # hot2Warm, warm2Hot, cold2Warm, warm2Cold, deleteWarm 都是中间状态
                
                # 等待下一次轮询
                time.sleep(SCAN_CONFIG['poll_interval'])
                retries += 1
                
            except Exception as e:
                logger.error(f"轮询任务状态失败: {str(e)}")
                time.sleep(SCAN_CONFIG['poll_interval'])
                retries += 1
        
        # 超时处理
        logger.warning(f"任务轮询超时: {workflow_id}")
        self.update_migration_record(record_id, status='failed', error_message='任务轮询超时', end_time=True)
        final_status = 'timeout'
        return final_status
    
    def run(self):
        """执行数据迁移流程"""
        try:
            # 连接数据库
            self.connect_db()
            
            # 获取数据集信息
            datasets = self.get_datasets()
            self.total_datasets = len(datasets)
            
            logger.info(f"任务开始: 共需处理 {self.total_datasets} 个数据集")
            
            # 初始化数据集统计信息
            for dataset in datasets:
                dataset_id = dataset['id']
                self.dataset_stats[dataset_id] = {
                    'total_paths': 0,          # 需要处理的路径总数
                    'processed_paths': 0,      # 已处理的路径数
                    'success_count': 0,        # 成功处理的路径数
                    'failed_count': 0,         # 处理失败的路径数
                    'base_path': dataset['first_path'].strip('"')  # 数据集基础路径
                }
            
            for idx, dataset in enumerate(datasets):
                dataset_id = dataset['id']
                base_path = dataset['first_path'].strip('"')
                
                logger.info(f"处理数据集 [{idx + 1}/{self.total_datasets}] ID={dataset_id}: {base_path}")
                
                # 扫描目录
                complete_paths = self.scan_directory_for_complete_files(base_path, self.months_back)
                self.dataset_stats[dataset_id]['total_paths'] = len(complete_paths)
                
                if len(complete_paths) == 0:
                    logger.warning(f"数据集 {dataset_id} 未找到需要处理的路径")
                    self.processed_datasets += 1
                    logger.info(f"数据集进度: {self.processed_datasets}/{self.total_datasets}")
                    continue
                
                logger.info(f"数据集 {dataset_id} 找到 {len(complete_paths)} 个路径需要处理")
                
                for path_idx, path in enumerate(complete_paths):
                    try:
                        path_progress = f"[{path_idx + 1}/{len(complete_paths)}]"
                        logger.info(f"处理路径 {path_progress} {path}")
                        
                        # 创建迁移记录
                        record_id = self.create_migration_record(dataset_id, path)
                        
                        # 创建备份任务
                        backup_result = self.create_backup_job(self.convert_path_for_api(path), dataset_id)
                        
                        # 从返回结果中提取workflow_id
                        workflow_id = None
                        
                        # 打印完整的返回结果，便于调试
                        logger.debug(f"备份任务返回结果: {backup_result}")
                        
                        if isinstance(backup_result, dict):
                            # 处理嵌套的数据结构
                            if 'data' in backup_result and isinstance(backup_result['data'], dict) and 'workflow_id' in backup_result['data']:
                                workflow_id = backup_result['data']['workflow_id']
                                logger.info(f"从data.workflow_id获取到ID: {workflow_id}")
                            # 处理直接返回ID的情况
                            elif 'id' in backup_result:
                                workflow_id = backup_result['id']
                                logger.info(f"从顶层id获取到ID: {workflow_id}")
                            # 处理其他可能的返回格式
                            elif 'workflow_id' in backup_result:
                                workflow_id = backup_result['workflow_id']
                                logger.info(f"从顶层workflow_id获取到ID: {workflow_id}")
                        
                        if not workflow_id:
                            logger.error(f"创建备份任务失败，未返回workflow_id: {backup_result}")
                            self.update_migration_record(record_id, status='failed', error_message='创建备份任务失败，未返回workflow_id', end_time=True)
                            self.dataset_stats[dataset_id]['failed_count'] += 1
                            continue
                        
                        logger.info(f"成功获取到workflow_id: {workflow_id}")
                        
                        # 更新工作流ID
                        self.update_migration_record(record_id, workflow_id=workflow_id)
                        
                        # 轮询任务状态
                        final_status = self.poll_job_status(record_id, workflow_id)
                        
                        # 更新统计信息
                        self.dataset_stats[dataset_id]['processed_paths'] += 1
                        
                        # 根据任务最终状态更新成功/失败计数
                        if final_status == 'completed':
                            self.dataset_stats[dataset_id]['success_count'] += 1
                            logger.info(f"任务成功完成")
                        else:
                            self.dataset_stats[dataset_id]['failed_count'] += 1
                            logger.info(f"任务执行失败，状态: {final_status}")
                        
                        # 显示当前路径进度
                        logger.info(f"路径处理进度: {path_idx + 1}/{len(complete_paths)}, " +
                                    f"成功: {self.dataset_stats[dataset_id]['success_count']}, " +
                                    f"失败: {self.dataset_stats[dataset_id]['failed_count']}")
                        
                    except Exception as e:
                        self.dataset_stats[dataset_id]['failed_count'] += 1
                        logger.error(f"处理路径失败 {path}: {str(e)}")
                        continue
                
                # 完成一个数据集的处理后，更新并打印进度
                self.processed_datasets += 1
                logger.info(f"数据集 {dataset_id} 处理完成: " +
                            f"总路径数: {self.dataset_stats[dataset_id]['total_paths']}, " +
                            f"处理成功: {self.dataset_stats[dataset_id]['success_count']}, " +
                            f"处理失败: {self.dataset_stats[dataset_id]['failed_count']}")
                logger.info(f"数据集总进度: {self.processed_datasets}/{self.total_datasets}")
            
            # 任务结束，输出总结信息
            self.print_summary()
            
        except Exception as e:
            logger.error(f"执行数据迁移流程失败: {str(e)}")
        finally:
            # 关闭数据库连接
            self.close_db()
    
    def print_summary(self):
        """打印任务执行总结信息"""
        try:
            logger.info("=" * 50)
            logger.info("数据迁移任务执行总结")
            logger.info("=" * 50)
            
            total_paths = 0
            total_success = 0
            total_failed = 0
            
            logger.info(f"数据集处理情况: {self.processed_datasets}/{self.total_datasets}")
            
            # 按数据集ID排序
            sorted_datasets = sorted(self.dataset_stats.items())
            
            for dataset_id, stats in sorted_datasets:
                total_paths += stats['total_paths']
                total_success += stats['success_count']
                total_failed += stats['failed_count']
                
                success_rate = 0
                if stats['total_paths'] > 0:
                    success_rate = (stats['success_count'] / stats['total_paths']) * 100
                
                logger.info(f"数据集 {dataset_id}: 路径总数={stats['total_paths']}, " +
                           f"成功={stats['success_count']}, 失败={stats['failed_count']}, " +
                           f"成功率={success_rate:.2f}%")
            
            # 总体成功率
            overall_success_rate = 0
            if total_paths > 0:
                overall_success_rate = (total_success / total_paths) * 100
                
            logger.info("-" * 50)
            logger.info(f"总计: 数据集数={self.total_datasets}, 路径总数={total_paths}, " +
                       f"成功总数={total_success}, 失败总数={total_failed}, " +
                       f"总体成功率={overall_success_rate:.2f}%")
            logger.info("=" * 50)
        except Exception as e:
            logger.error(f"生成任务总结失败: {str(e)}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据迁移自动化脚本')
    parser.add_argument('--env', choices=['dev', 'prod'], default='dev', help='运行环境 (dev 或 prod)')
    parser.add_argument('--debug', action='store_true', help='调试模式，不实际调用API')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='日志级别')
    parser.add_argument('--months-back', type=int, default=1, help='向前扫描的月份数，默认为1（上个月）。例如：设置为2表示扫描前2个月的数据')
    args = parser.parse_args()

    # 设置日志级别
    logging.getLogger('data_migration').setLevel(getattr(logging, args.log_level))

    logger.info(f"开始执行数据迁移任务，环境: {args.env}, 调试模式: {'开启' if args.debug else '关闭'}, 扫描前{args.months_back}个月")
    logger.info(f"使用示例: python data_migration.py --env dev --debug --months-back 2 --log-level DEBUG")

    service = DataMigrationService(env=args.env, debug_mode=args.debug, months_back=args.months_back)
    service.run()

    logger.info("数据迁移任务执行完成")

    # 任务完成后重命名日志文件
    rename_log_file()

if __name__ == '__main__':
    import argparse
    main() 