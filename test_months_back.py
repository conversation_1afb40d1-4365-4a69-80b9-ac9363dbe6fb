#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试months_back参数功能的脚本
"""

import datetime
import calendar

def test_months_back_logic(months_back=1):
    """测试向前扫描月份的逻辑"""
    today = datetime.datetime.now()
    scan_dates = []
    target_years = set()
    
    print(f"当前时间: {today.strftime('%Y年%m月%d日')}")
    print(f"扫描前{months_back}个月的数据")
    print("-" * 50)
    
    for month_offset in range(1, months_back + 1):
        # 计算目标月份
        target_month = today.month - month_offset
        target_year = today.year
        
        # 处理跨年情况
        while target_month <= 0:
            target_month += 12
            target_year -= 1
        
        # 获取该月的天数
        last_day_of_month = calendar.monthrange(target_year, target_month)[1]
        
        # 生成该月每一天的日期字符串
        month_dates = []
        for day in range(1, last_day_of_month + 1):
            date_obj = datetime.datetime(target_year, target_month, day)
            month_dates.append(date_obj.strftime('%Y%m%d'))
        
        scan_dates.extend(month_dates)
        target_years.add(str(target_year))
        
        print(f"月份 {month_offset}: {target_year}年{target_month}月 ({len(month_dates)}天)")
        print(f"  日期范围: {month_dates[0]} 至 {month_dates[-1]}")
    
    # 转换为列表并排序
    target_years = list(target_years)
    scan_dates.sort()
    
    print("-" * 50)
    print(f"总扫描日期范围: {scan_dates[0]} 至 {scan_dates[-1]}")
    print(f"总天数: {len(scan_dates)}天")
    print(f"涉及年份: {target_years}")
    print("=" * 50)

if __name__ == '__main__':
    print("测试不同months_back参数的效果:")
    print()
    
    # 测试默认值（上个月）
    print("1. 默认参数 months_back=1 (只扫描上个月):")
    test_months_back_logic(1)
    print()
    
    # 测试您的需求（前2个月）
    print("2. 您的需求 months_back=2 (扫描前2个月，即5月和6月):")
    test_months_back_logic(2)
    print()
    
    # 测试更多月份
    print("3. 扩展测试 months_back=3 (扫描前3个月):")
    test_months_back_logic(3)
